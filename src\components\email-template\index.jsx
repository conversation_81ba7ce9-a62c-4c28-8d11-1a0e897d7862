import { Lightbulb, Users, PackageCheck, Globe } from "lucide-react";
import img from "../../assets/images/2nd.webp";

export default function EmailTemplate() {
  return (
    <section className="relative z-0">
      {/* Background Section with overlay */}
      <div
        className="pt-20 pb-80 px-6 md:px-20 text-white relative z-0"
        style={{
          backgroundImage: `linear-gradient(rgba(206, 22, 27, 0.85), rgba(206, 22, 27, 0.85)), url(${img})`,
          backgroundSize: "cover",
          backgroundPosition: "center",
        }}
      >
        <div className="max-w-screen-xl mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold leading-snug mb-4">
            Customize Large Scale Mining Equipment With Affordable Cost
          </h2>
          <p className="text-sm md:text-base max-w-2xl mx-auto">
            STCrushers provides high quality and reliable mining equipment
            customer manufacturing service for global customers, we design the
            production line according to the needs of the customer's processed
            materials and output, and realize fully automatic production from
            feed to discharge.
          </p>
        </div>

        {/* Feature Cards */}
        <div className="max-w-screen-xl mx-auto mt-14 grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          {[
            {
              icon: (
                <Lightbulb className="text-[#ce161b] bg-white rounded-full p-2 w-10 h-10" />
              ),
              title: "20 Years Of Experience",
              desc: "We have more than 20 years of experience in the production of large mining equipment.",
            },
            {
              icon: (
                <Users className="text-[#ce161b] bg-white rounded-full p-2 w-10 h-10" />
              ),
              title: "Professional Production And R&D Team",
              desc: "An experienced production line design team for different materials.",
            },
            {
              icon: (
                <PackageCheck className="text-[#ce161b] bg-white rounded-full p-2 w-10 h-10" />
              ),
              title: "Advanced Production Facilities",
              desc: "We have two large workshops and various machine tools.",
            },
            {
              icon: (
                <Globe className="text-[#ce161b] bg-white rounded-full p-2 w-10 h-10" />
              ),
              title: "Competitive Prices",
              desc: "We offer preferential prices and flexible payment methods.",
            },
          ].map((item, idx) => (
            <div
              key={idx}
              className="bg-white text-black rounded-lg p-6 shadow-md relative overflow-hidden transition-transform duration-300 transform hover:-translate-y-2 hover:shadow-xl group "
            >
              <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-r from-transparent via-[#ce161b]/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 animate-pulse" />
              <div>{item.icon}</div>
              <h4 className="font-semibold mt-6 mb-2 text-lg min-h-[48px]">
                {item.title}
              </h4>
              <p className="text-sm text-gray-600 leading-relaxed min-h-[60px]">
                {item.desc}
              </p>
            </div>
          ))}
        </div>
      </div>

      {/* Inquiry Form (Overlapping) */}
      <div className="relative z-10 -mt-50">
        <div className="bg-white rounded-xl shadow-xl max-w-screen-md mx-auto px-6 md:px-12 py-10">
          <h3 className="text-xl font-semibold text-[#1c1a30] mb-6">
            Send An Inquiry
          </h3>
          <form className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <input
              type="text"
              placeholder="Name"
              className="input input-bordered w-full"
            />
            <input
              type="text"
              placeholder="Phone"
              className="input input-bordered w-full"
            />
            <input
              type="email"
              placeholder="Email"
              className="input input-bordered w-full"
            />
            <input
              type="text"
              placeholder="Country"
              className="input input-bordered w-full"
            />
            <textarea
              placeholder="Describe what you need"
              className="textarea textarea-bordered col-span-1 md:col-span-2"
              rows={4}
            ></textarea>
            <button
              type="submit"
              className="btn bg-[#ce161b] text-white hover:opacity-90 col-span-1 md:col-span-2"
            >
              Contact Us
            </button>
          </form>
        </div>
      </div>

      {/* Company Stats */}
      <div className="mt-28 pb-20 px-6 md:px-20">
        <div className="max-w-screen-xl mx-auto grid grid-cols-2 sm:grid-cols-4 gap-10 text-center">
          {[
            {
              label: "Global Projects In 2024",
              icon: <Globe size={26} className="text-[#ce161b]" />,
            },
            {
              label: "Customers In China And Abroad",
              icon: <PackageCheck size={26} className="text-[#ce161b]" />,
            },
            {
              label: "Professional Team Members",
              icon: <Users size={26} className="text-[#ce161b]" />,
            },
            {
              label: "Years Of Experience",
              icon: <Lightbulb size={26} className="text-[#ce161b]" />,
            },
          ].map((stat, idx) => (
            <div key={idx} className="flex flex-col items-center">
              <div className="bg-[#ce161b]/10 rounded-full w-14 h-14 flex items-center justify-center mb-3">
                {stat.icon}
              </div>
              <h4 className="text-3xl font-bold text-[#1c1a30]">0</h4>
              <p className="text-xs text-gray-600 mt-1">{stat.label}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
