import { Lightbulb, Users, PackageCheck, Globe } from "lucide-react";
import img from "../../assets/images/2nd.webp";

export default function EmailTemplate() {
  return (
    <section className="relative z-0">
      {/* Background Section with overlay */}
      <div
        className="pt-20 pb-80 px-6 md:px-20 text-white relative z-0"
        style={{
          backgroundImage: `linear-gradient(rgba(206, 22, 27, 0.85), rgba(206, 22, 27, 0.85)), url(${img})`,
          backgroundSize: "cover",
          backgroundPosition: "center",
        }}
      >
        <div className="max-w-screen-xl mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold leading-snug mb-6">
            Custom Mining Equipment Solutions at Competitive Prices
          </h2>
          <p className="text-base md:text-lg max-w-3xl mx-auto leading-relaxed">
            STCrushers delivers premium mining equipment and custom
            manufacturing services to clients worldwide. Our expert engineering
            team designs tailored production lines based on your specific
            material processing requirements and output targets, ensuring
            seamless automation from material feed to final discharge.
          </p>
        </div>

        {/* Feature Cards */}
        <div className="max-w-screen-xl mx-auto mt-14 grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          {[
            {
              icon: (
                <Lightbulb className="text-[#ce161b] bg-white rounded-full p-2 w-10 h-10" />
              ),
              title: "20 Years Of Experience",
              desc: "We have more than 20 years of experience in the production of large mining equipment.",
            },
            {
              icon: (
                <Users className="text-[#ce161b] bg-white rounded-full p-2 w-10 h-10" />
              ),
              title: "Professional Production And R&D Team",
              desc: "An experienced production line design team for different materials.",
            },
            {
              icon: (
                <PackageCheck className="text-[#ce161b] bg-white rounded-full p-2 w-10 h-10" />
              ),
              title: "Advanced Production Facilities",
              desc: "We have two large workshops and various machine tools.",
            },
            {
              icon: (
                <Globe className="text-[#ce161b] bg-white rounded-full p-2 w-10 h-10" />
              ),
              title: "Competitive Prices",
              desc: "We offer preferential prices and flexible payment methods.",
            },
          ].map((item, idx) => (
            <div
              key={idx}
              className="bg-white text-black rounded-lg p-6 shadow-md relative overflow-hidden transition-transform duration-300 transform hover:-translate-y-2  group "
            >
              <div className="absolute top-0 left-0 w-full h-full " />
              <div>{item.icon}</div>
              <h4 className="font-semibold mt-6 mb-2 text-lg min-h-[48px]">
                {item.title}
              </h4>
              <p className="text-sm text-gray-600 leading-relaxed min-h-[60px]">
                {item.desc}
              </p>
            </div>
          ))}
        </div>
      </div>

      {/* Inquiry Form (Overlapping) */}
      <div className="relative z-10 -mt-50">
        <div className="bg-white rounded-xl shadow-xl max-w-screen-md mx-auto px-6 md:px-12 py-12">
          <div className="text-center mb-8">
            <h3 className="text-2xl font-bold text-[#1c1a30] mb-3">
              Request a Professional Consultation
            </h3>
            <p className="text-gray-600 text-sm">
              Get expert advice on your mining equipment requirements
            </p>
          </div>
          <form className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">
                Full Name *
              </label>
              <input
                type="text"
                placeholder="Enter your full name"
                className="input input-bordered w-full h-12 px-4 border-gray-300 focus:border-[#ce161b] focus:ring-1 focus:ring-[#ce161b] transition-colors"
                required
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">
                Phone Number *
              </label>
              <input
                type="tel"
                placeholder="Enter your phone number"
                className="input input-bordered w-full h-12 px-4 border-gray-300 focus:border-[#ce161b] focus:ring-1 focus:ring-[#ce161b] transition-colors"
                required
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">
                Email Address *
              </label>
              <input
                type="email"
                placeholder="Enter your email address"
                className="input input-bordered w-full h-12 px-4 border-gray-300 focus:border-[#ce161b] focus:ring-1 focus:ring-[#ce161b] transition-colors"
                required
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">
                Country/Region
              </label>
              <input
                type="text"
                placeholder="Enter your country"
                className="input input-bordered w-full h-12 px-4 border-gray-300 focus:border-[#ce161b] focus:ring-1 focus:ring-[#ce161b] transition-colors"
              />
            </div>
            <div className="space-y-2 col-span-1 md:col-span-2">
              <label className="text-sm font-medium text-gray-700">
                Project Requirements *
              </label>
              <textarea
                placeholder="Please describe your mining equipment needs, project specifications, and any specific requirements..."
                className="textarea textarea-bordered w-full px-4 py-3 border-gray-300 focus:border-[#ce161b] focus:ring-1 focus:ring-[#ce161b] transition-colors resize-none"
                rows={5}
                required
              ></textarea>
            </div>
            <button
              type="submit"
              className="btn bg-[#ce161b] text-white hover:bg-[#b01419] col-span-1 md:col-span-2 h-12 text-base font-semibold transition-colors duration-200 shadow-lg hover:shadow-xl"
            >
              Submit Inquiry
            </button>
          </form>
        </div>
      </div>

      {/* Company Stats */}
      <div className="mt-28 pb-20 px-6 md:px-20">
        <div className="max-w-screen-xl mx-auto grid grid-cols-2 sm:grid-cols-4 gap-10 text-center">
          {[
            {
              number: "150+",
              label: "Global Projects In 2024",
              icon: <Globe size={26} className="text-[#ce161b]" />,
            },
            {
              number: "500+",
              label: "Customers In China And Abroad",
              icon: <PackageCheck size={26} className="text-[#ce161b]" />,
            },
            {
              number: "80+",
              label: "Professional Team Members",
              icon: <Users size={26} className="text-[#ce161b]" />,
            },
            {
              number: "20+",
              label: "Years Of Experience",
              icon: <Lightbulb size={26} className="text-[#ce161b]" />,
            },
          ].map((stat, idx) => (
            <div key={idx} className="flex flex-col items-center">
              <div className="bg-[#ce161b]/10 rounded-full w-14 h-14 flex items-center justify-center mb-3">
                {stat.icon}
              </div>
              <h4 className="text-3xl font-bold text-[#1c1a30]">
                {stat.number}
              </h4>
              <p className="text-xs text-gray-600 mt-1">{stat.label}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
